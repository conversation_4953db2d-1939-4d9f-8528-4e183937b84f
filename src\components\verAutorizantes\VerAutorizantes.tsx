import React, { useState, useEffect } from 'react';
import { getUrlSeguimientoFactura } from '../../utilities/contextInfo';
import { useProveedor } from '../contextProvider/datosProveedorContext';

const VerAutorizantes = () => {
  const { state } = useProveedor();
  const [autorizantes, setAutorizantes] = useState([]);

  useEffect(() => {
    obtenerListaTareas(state.operacionClienteNumero, "Tareas Seguimiento Facturas");
  }, []);

  const obtenerListaTareas = async (item: any, lista: any) => {
    try {
      const filtro =
        `((Status eq 'No iniciada' or Status eq 'En curso') and substringof('${item}', Title))`;
      const response = await fetch(
        `${getUrlSeguimientoFactura()}/_api/web/lists/GetByTitle('${lista}')/items?$filter=${filtro}`,
        {
          method: "GET",
          headers: {
            Accept: "application/json;odata=verbose",
            "Content-Type": "application/json;odata=verbose",
          },
        }
      );
      const data = await response.json();
      const items = data.d.results;
      if (items.length > 0) {
        renderVerAutorizante(items);
      }
    } catch (error) {
      console.log("Error al obtener los detalles:", error);
    }
  };

  const renderVerAutorizante = async (results: any) => {
    // Reemplazar jQuery.ajax con fetch nativo y Promise.all para manejar múltiples requests
    const userPromises = results.map(async (item: any) => {
      try {
        const response = await fetch(
          `${getUrlSeguimientoFactura()}/_api/web/getuserbyid(${item.AssignedToId})`,
          {
            method: "GET",
            headers: {
              Accept: "application/json;odata=verbose",
              "Content-Type": "application/json;odata=verbose",
            },
          }
        );
        const data = await response.json();
        if (data.d.Title !== "ADMINISTRADOR SHAREPOINT") {
          return data.d.Title;
        }
        return null;
      } catch (error) {
        console.log("Error en renderVerAutorizante:", error);
        return null;
      }
    });

    // Esperar a que todas las promesas se resuelvan
    const userTitles = await Promise.all(userPromises);
    const filteredTitles = userTitles.filter(title => title !== null);

    setAutorizantes(filteredTitles);
  };

  console.log("autorizantes:", autorizantes);

  return (
    <div className='position-relative hover-container'>
      <style>
        {`
          .hover-container:hover .hover-list {
            display: block !important;
          }
        `}
      </style>
      <p style={{ cursor: 'pointer' }} className='font-weight-bold ml-5 p-2'>
        Ver Autorizantes
      </p>
      {autorizantes.length > 0 ? (
        <div
          className='position-absolute hover-list'
          style={{
            display: 'none',
            top: '100%',
            left: '0',
            backgroundColor: 'black',
            color: 'white',
            border: 'solid 1px #ccc',
            boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
            padding: '10px',
            zIndex: '10',
            paddingLeft: '20px',
            paddingRight: '20px',
            right: '-100%',
            borderRadius: '5px',
          }}
        >
          <ol>
            {autorizantes.map((autorizante, index) => (
              <li key={index}>{autorizante}</li>
            ))}
          </ol>
        </div>
      ) : (
        <p style={{
          cursor: 'pointer',
          display: 'none',
          top: '100%',
          left: '0',
          backgroundColor: 'black',
          color: 'white',
          border: 'solid 1px #ccc',
          boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
          padding: '10px',
          zIndex: '10',
          paddingLeft: '20px',
          paddingRight: '20px',
          right: '-100%',
          borderRadius: '5px',
        }} className='position-absolute ml-5 p-2 hover-list'>No se encuentran autorizantes.</p>
      )}
    </div>
  );
};

export default VerAutorizantes;