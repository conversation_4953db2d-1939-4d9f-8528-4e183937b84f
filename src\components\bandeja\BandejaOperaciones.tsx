import React, { useState, useEffect } from 'react';

interface BandejaOperacionesProps {
  previewContainerId: string;
  defaultContainerId: string;
}

const BandejaOperaciones: React.FC<BandejaOperacionesProps> = ({
  previewContainerId,
  defaultContainerId
}) => {
  const [selectedUsername, setSelectedUsername] = useState<string>('');
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const [currentView, setCurrentView] = useState<'default' | 'details' | 'documents'>('default');

  useEffect(() => {
    // Inicializar event listeners para elementos .tbl-item
    const handleItemClick = (event: Event) => {
      const target = event.currentTarget as HTMLElement;
      
      // Remover clase selected de todos los elementos
      document.querySelectorAll('.tbl-item').forEach(item => {
        item.classList.remove('tbl-item--selected');
      });
      
      // Agregar clase selected al elemento clickeado
      target.classList.add('tbl-item--selected');
      
      // Obtener el nombre del usuario
      const nameElement = target.querySelector('.tbl-item__name') as HTMLElement;
      if (nameElement) {
        const username = nameElement.innerText;
        setSelectedUsername(username);
        showDetailsView(username);
      }
    };

    // Agregar event listeners a todos los elementos .tbl-item
    const tblItems = document.querySelectorAll('.tbl-item');
    tblItems.forEach(item => {
      item.addEventListener('click', handleItemClick);
    });

    // Cleanup function
    return () => {
      tblItems.forEach(item => {
        item.removeEventListener('click', handleItemClick);
      });
    };
  }, []);

  const closePreview = () => {
    const previewDiv = document.getElementById(previewContainerId);
    if (previewDiv) {
      previewDiv.classList.remove('preview--show');
    }
    
    // Remover clase selected de todos los elementos
    document.querySelectorAll('.tbl-item').forEach(item => {
      item.classList.remove('tbl-item--selected');
    });
    
    setShowPreview(false);
    showDefaultView();
  };

  const showDetailsView = (username: string) => {
    const defaultDiv = document.getElementById(defaultContainerId);
    const previewDiv = document.getElementById(previewContainerId);
    
    if (defaultDiv) {
      defaultDiv.classList.remove('default--show');
    }
    
    if (previewDiv) {
      previewDiv.classList.add('preview--show');
      previewDiv.innerHTML = generateOperacionHTML(username);
    }
    
    setCurrentView('details');
    setShowPreview(true);
  };

  const showDocumentsView = () => {
    const defaultDiv = document.getElementById(defaultContainerId);
    const previewDiv = document.getElementById(previewContainerId);
    
    if (defaultDiv) {
      defaultDiv.classList.remove('default--show');
    }
    
    if (previewDiv) {
      previewDiv.classList.add('preview--show');
      previewDiv.innerHTML = generateDocumentosHTML();
    }
    
    setCurrentView('documents');
  };

  const showDefaultView = () => {
    const defaultDiv = document.getElementById(defaultContainerId);
    if (defaultDiv) {
      defaultDiv.classList.add('default--show');
      defaultDiv.innerHTML = generateDefaultContent();
    }
    setCurrentView('default');
  };

  const showLoaderView = () => {
    const defaultDiv = document.getElementById(defaultContainerId);
    if (defaultDiv) {
      defaultDiv.classList.add('default--show');
      defaultDiv.innerHTML = generateDefaultLoader();
    }
  };

  const generateDefaultContent = () => {
    return `
      <div class="text-center">
        <i class="fa fa-file fa-2x mb-3"></i>
        <h5 class="mb-0">Seleccione una Operación</h5>
        <p>Los detalles se visualizan aqui</p>
      </div>
    `;
  };

  const generateDefaultLoader = () => {
    return `
      <div class="text-center">
        <i class="fa fa-sync-alt fa-spin fa-2x mb-3"></i>
        <p>Obteniendo datos...</p>
      </div>
    `;
  };

  const generateMultiSelect = (number: number = 2) => {
    return `
      <div class="preview__default d-flex align-items-center justify-content-center">
        <div class="text-center">
          <i class="fa fa-copy fa-2x mb-3"></i>
          <h5 class="mb-0">${number} Operaciones Seleccionadas</h5>
          <p><a href="#">Seleccionar todas</a> las operaciones</p>
          <div class="text-left px-5">
            <a href="#" class="d-block mb-2"><i class="fa fa-check"></i> Aprobar</a>
            <a href="#" class="d-block mb-2"><i class="fa fa-times"></i> Rechazar</a>
          </div>
        </div>
      </div>
    `;
  };

  const generateOperacionHTML = (username: string) => {
    return `
      <div class="px-4 py-3 d-flex justify-content-between bg-white sticky-top">
        <div class="d-flex align-items-center">
          <span onclick="closePreview()" class="pw-close">
            <span class="pw-close__bar-1"></span>
            <span class="pw-close__bar-2"></span>
          </span>
          <p class="ml-3 mb-0">Operación No. 5750131219</p>
        </div>
        <div class="btn-group">
          <a href="#" class="btn btn-outline-fun-blue">Rechazar</a>
          <a href="#" class="btn btn-fun-blue">Aprobar</a>
        </div>
      </div>
      <div class="prw-cont__detail px-4">
        <div class="row mb-4">
          <div class="col-12 col-md-4">
            <figure class="avatar avatar--lg">
              <img src="https://randomuser.me/api/portraits/men/1.jpg">
            </figure>
            <div class="mt-2 mb-2">
              <h5 class="mb-0">${username}</h5>
              <p class="mb-0">Clasificación 1</p>
              <p class="mb-0">Código: 410241</p>
              <p class="mb-0">Ref. Atraso: BUENA</p>
            </div>
            <div class="form-group">
              <button onclick="showDocumentsView()" class="btn btn-secondary">Ver documentos</button>
            </div>
          </div>
          <div class="col-12 col-md-8">
            <div class="row">
              <div class="col-12">
                <p>Primer crédito hace 2 años</p>
              </div>
              <div class="col-6">
                <div class="client-data">
                  <b>Documento</b>
                  <p>CI - 5.864.546</p>
                </div>
              </div>
              <div class="col-6">
                <div class="client-data">
                  <b>Email</b>
                  <p><EMAIL> <span class="badge badge-warning">Nuevo</span></p>
                </div>
              </div>
              <div class="col-6">
                <div class="client-data">
                  <b>Celular</b>
                  <p>0984893626 <span class="badge badge-warning">Nuevo</span></p>
                </div>
              </div>
              <div class="col-6">
                <div class="client-data">
                  <b>Estado Civil</b>
                  <p>Soltero</p>
                </div>
              </div>
              <div class="col-6">
                <div class="client-data">
                  <b>Sucursal</b>
                  <p>Matriz</p>
                </div>
              </div>
              <div class="col-6">
                <div class="client-data">
                  <b>Oficial</b>
                  <p>Nombre Ejemplo</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Tabs y contenido adicional aquí -->
      </div>
    `;
  };

  const generateDocumentosHTML = () => {
    return `
      <div class="px-4 py-3 d-flex justify-content-between bg-white sticky-top">
        <div class="d-flex align-items-center">
          <span onclick="closePreview()" class="pw-close">
            <span class="pw-close__bar-1"></span>
            <span class="pw-close__bar-2"></span>
          </span>
          <p class="ml-3 mb-0">Solicitud No. 5750131219</p>
        </div>
        <div class="btn-group">
          <a href="#" class="btn btn-outline-fun-blue">Rechazar</a>
          <a href="#" class="btn btn-fun-blue">Aprobar</a>
        </div>
      </div>
      <div class="prw-cont__detail px-4">
        <div class="pt-5 pb-3">
          <h4 class="text-center">Documentos</h4>
          <button onclick="showDetailsView()" class="btn btn-link"><i class="fa fa-chevron-left"></i> Operación</button>
        </div>
        <!-- Contenido de documentos aquí -->
      </div>
    `;
  };

  // Exponer funciones globalmente para compatibilidad con HTML existente
  useEffect(() => {
    (window as any).closePreview = closePreview;
    (window as any).showDetailsView = showDetailsView;
    (window as any).showDocumentsView = showDocumentsView;
    (window as any).showDefaultView = showDefaultView;
    (window as any).showLoaderView = showLoaderView;
  }, []);

  return null; // Este componente no renderiza nada, solo maneja la lógica
};

export default BandejaOperaciones;
