// Exportar todos los componentes UI nativos
export { default as Modal } from './Modal';
export { default as Tabs } from './Tabs';
export { default as Dropdown } from './Dropdown';
export { default as Tooltip } from './Tooltip';
export { default as Collapse, Collapsible, useCollapse } from './Collapse';

// Tipos para facilitar el uso
export type { default as ModalProps } from './Modal';

// Re-exportar tipos comunes
export interface TabItem {
  id: string;
  label: string;
  content: React.ReactNode;
  disabled?: boolean;
}

export interface DropdownItem {
  id: string;
  label: string;
  onClick?: () => void;
  disabled?: boolean;
  divider?: boolean;
}
