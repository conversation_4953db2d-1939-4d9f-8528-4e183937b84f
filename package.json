{"name": "front-bpm-consola", "version": "1.0.0", "description": "TODO: Give a short introduction of your project. Let this section explain the objectives or the motivation behind this project.", "private": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://<EMAIL>/BancoContinental/Procesos%20Digitales/_git/front-bpm-consola"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.19.6", "@babel/plugin-transform-typescript": "^7.20.0", "@babel/preset-env": "^7.19.4", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@types/bootstrap": "^5.2.10", "@types/jquery": "^3.5.30", "@types/lodash": "^4.14.186", "@types/react": "^18.0.24", "@types/react-datepicker": "^4.19.3", "@types/react-dom": "^18.0.8", "@types/react-table": "^7.7.12", "babel-loader": "^9.0.1", "css-loader": "^6.8.1", "style-loader": "^3.3.4", "ts-loader": "^9.4.1", "typescript": "^4.8.4", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.14.20", "@popperjs/core": "^2.11.8", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "axios": "^1.1.3", "babel-cli": "^6.26.0", "babel-preset-react-app": "^3.1.2", "bootstrap": "^5.3.3", "buffer": "^6.0.3", "formik": "^2.4.6", "jquery": "^3.7.1", "loaders.css": "^0.1.2", "lodash": "^4.17.21", "moment": "^2.29.4", "n-ary-tree": "^0.4.0", "react": "^18.2.0", "react-bootstrap": "^2.9.1", "react-datepicker": "^4.21.0", "react-dom": "^18.2.0", "react-number-format": "^5.3.1", "react-router-dom": "^6.11.2", "react-table": "^7.8.0", "react-toastify": "^10.0.5", "reactstrap": "^9.2.2", "stream": "^0.0.2", "stream-browserify": "^3.0.0", "sweetalert2": "^11.11.0", "sweetalert2-react-content": "^5.0.7", "xml-js": "^1.6.11"}, "main": "webpack.config.js", "directories": {"lib": "lib"}, "presets": ["@babel/preset-typescript"]}