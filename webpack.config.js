const path = require('path');

module.exports = {
  mode: 'development',
  entry: {
    list: './Maqueta/js/dist/list.min.js',
    finasys: './Maqueta/js/finansys.js',
    index: './src/index.tsx',
  },
  module: {
    rules: [
      {
        test: /\.(j|t)sx?$/,
        exclude: /node_modules/,
        use: [
          {
            loader: "babel-loader",
            options: {
              presets: [
                "@babel/env",
                "@babel/react",
                "@babel/typescript"
              ]
            }
          }
        ]
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
        exclude: /node_modules/
      }
    ],
  },

  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.jsx', '.d.ts'],
  },
  output: {
    filename: '[name].main.js',
    path: path.resolve(__dirname, './Maqueta/sites/consolaBPMdist'),
  },
};